# Traditional Indian SVG Patterns - Open Source & Free to Use

## 📜 License & Usage
All patterns in this project are **completely free and open source**. They are created as original SVG designs inspired by traditional Indian motifs and are available under **CC0 (Creative Commons Zero)** license - meaning you can:

- ✅ Use commercially
- ✅ Modify and adapt
- ✅ Redistribute
- ✅ Use without attribution (though appreciated)

## 🎨 Pattern Collection

### 1. **Mango Leaf Pattern (आम्रपत्र)**
- **File**: Traditional Golden template
- **Inspiration**: Sacred mango leaves used in Indian ceremonies
- **Elements**: Stylized mango leaf shapes with decorative dots
- **Colors**: Golden tones with transparency
- **Usage**: Wedding invitations, religious ceremonies

### 2. **Paisley Motif (पैस्ले)**
- **File**: Royal Maroon template  
- **Inspiration**: Traditional paisley (boteh) patterns from Persian/Indian heritage
- **Elements**: Teardrop shapes with inner decorative details
- **Colors**: Gold on maroon background
- **Usage**: Royal ceremonies, elegant invitations

### 3. **Lotus Mandala (कमल मंडल)**
- **File**: Elegant Saffron template
- **Inspiration**: Sacred lotus flower in mandala form
- **Elements**: 8-petaled lotus with concentric circles
- **Colors**: Saffron and brown tones
- **Usage**: Spiritual ceremonies, meditation themes

### 4. **Rangoli Pink (रांगोळी गुलाबी)**
- **File**: Rangoli Pink template
- **Inspiration**: Traditional Rangoli patterns from Vecteezy collection
- **Elements**: 8-petaled flower with concentric rings and decorative dots
- **Colors**: Pink and magenta tones
- **Usage**: Festival celebrations, modern weddings

### 5. **Peacock Feather (मोरपंख)**
- **File**: Peacock Green template
- **Inspiration**: Sacred peacock feathers from Lord Krishna's crown
- **Elements**: Detailed peacock eye with feather barbs
- **Colors**: Teal, green, and gold
- **Usage**: Krishna-themed ceremonies, traditional weddings

### 6. **Temple Geometry (मंदिर ज्यामिती)**
- **File**: Temple Serenity template
- **Inspiration**: Sacred geometry found in Indian temple architecture
- **Elements**: Squares, diamonds, and circles in geometric harmony
- **Colors**: Purple and gold
- **Usage**: Religious ceremonies, temple events

### 7. **Marigold Flowers (गेंदाफूल)**
- **File**: Floral Bliss template
- **Inspiration**: Marigold flowers used in Indian celebrations
- **Elements**: Multi-petaled flowers with leaves
- **Colors**: Orange, pink, and green
- **Usage**: Festival invitations, celebration cards

### 8. **Traditional Border (पारंपारिक सीमा)**
- **File**: Classic Cream template
- **Inspiration**: Classic Indian border designs
- **Elements**: Diamond shapes with decorative lines
- **Colors**: Brown and cream tones
- **Usage**: Traditional ceremonies, classic invitations

### 9. **Festival Diwali (दिवाळी उत्सव)**
- **File**: Festival Diwali template
- **Inspiration**: Diwali rangoli patterns inspired by Vecteezy designs
- **Elements**: Concentric diya (lamp) patterns with floral motifs
- **Colors**: Orange, yellow, and deep red
- **Usage**: Diwali celebrations, festival invitations

## 🛠️ Technical Details

### SVG Structure
All patterns are created as:
- **Inline SVG**: Embedded as base64 data URIs
- **Scalable**: Vector-based for crisp rendering at any size
- **Lightweight**: Optimized for web performance
- **Repeatable**: Designed as seamless patterns

### Pattern Specifications
- **Format**: SVG 1.1 compliant
- **Size**: 100x100 to 120x120 pixel base units
- **Colors**: RGBA with transparency for overlay effects
- **Compatibility**: Works in all modern browsers

### Implementation
```javascript
// Example usage in CSS
background-image: url('data:image/svg+xml;base64,[pattern-data]');
background-repeat: repeat;
background-blend-mode: overlay;
```

## 🎯 Cultural Significance

### Traditional Elements Used:
1. **Mango Leaves**: Symbol of prosperity and fertility
2. **Paisley**: Represents life and eternity
3. **Lotus**: Symbol of purity and enlightenment
4. **Peacock**: Associated with Lord Krishna and beauty
5. **Geometric Patterns**: Represent cosmic order and harmony
6. **Marigolds**: Flowers of celebration and joy

### Color Symbolism:
- **Gold**: Prosperity, divinity, auspiciousness
- **Saffron**: Purity, spirituality, sacrifice
- **Maroon**: Strength, courage, passion
- **Green**: Nature, new beginnings, harmony
- **Purple**: Royalty, spirituality, wisdom

## 📚 Sources of Inspiration

These patterns are original creations inspired by:
- Traditional Indian textile designs
- Temple architecture motifs
- Classical manuscript illuminations
- Folk art traditions
- Regional craft patterns

**Note**: While inspired by traditional motifs, all SVG code is original and created specifically for this project.

## 🤝 Contributing

Want to add more traditional patterns? 
1. Create original SVG designs inspired by Indian traditions
2. Ensure they're your own work or public domain
3. Follow the same technical specifications
4. Include cultural context and significance
5. Submit via pull request

## 📞 Support

For questions about pattern usage or cultural significance:
- Check the documentation
- Review the SVG source code
- Understand the cultural context provided

---

**Created with respect for Indian cultural heritage and traditions** 🙏

*These patterns celebrate the rich artistic legacy of India while being freely available for modern digital use.*
