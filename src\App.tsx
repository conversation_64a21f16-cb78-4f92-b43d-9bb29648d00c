import React, { useState, useRef, useEffect } from 'react';
import { Download, Eye, Palette, Users, Calendar, MapPin, Clock, Heart, Gift, Music, Sparkles } from 'lucide-react';

interface InvitationData {
  brideName: string;
  groomName: string;
  ceremonyType: string;
  date: string;
  time: string;
  venue: string;
  address: string;
  hostFamily: string;
  additionalInfo: string;
  selectedGanesha: string;
  // Dynamic fields based on ceremony
  fatherName?: string;
  motherName: string;
  groomFatherName?: string;
  groomMotherName?: string;
  mehendi?: {
    date: string;
    time: string;
    venue: string;
  };
  haldi?: {
    date: string;
    time: string;
    venue: string;
  };
  reception?: {
    date: string;
    time: string;
    venue: string;
  };
}

interface Template {
  id: string;
  name: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    text: string;
  };
  pattern: string;
  backgroundImage?: string;
  borderStyle?: string;
  decorativeElements?: string[];
}

const templates = [
  {
    id: 'traditional',
    name: '<PERSON> <PERSON> (पारंपारिक सोनेरी)',
    colors: {
      primary: '#B8860B', // Dark gold for elegance
      secondary: '#FFD700', // Bright gold for vibrancy
      accent: '#8B4513', // Saffron brown for contrast
      text: '#2C2C2C' // Dark gray for readable Marathi text
    },
    pattern: 'linear-gradient(135deg, #FFD700 0%, #B8860B 50%, #8B4513 100%)',
    backgroundImage: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxwYXR0ZXJuIGlkPSJtYW5nb0xlYWYiIHBhdHRlcm5Vbml0cz0idXNlclNwYWNlT25Vc2UiIHdpZHRoPSI1MCIgaGVpZ2h0PSI1MCI+CiAgICAgIDxwYXRoIGQ9Ik0yNSA1QzM1IDEwIDQwIDIwIDM1IDMwQzMwIDQwIDIwIDQ1IDEwIDQwQzUgMzAgMTAgMjAgMTUgMTBDMjAgNSAyNSA1IDI1IDVaIiBmaWxsPSJyZ2JhKDE4NCwxMzQsMTEsLjEyKSIgc3Ryb2tlPSJyZ2JhKDE4NCwxMzQsMTEsLjA4KSIgc3Ryb2tlLXdpZHRoPSIxIi8+CiAgICAgIDxjaXJjbGUgY3g9IjI1IiBjeT0iMjUiIHI9IjMiIGZpbGw9InJnYmEoMTg0LDEzNCwxMSwuMTUpIi8+CiAgICAgIDxwYXRoIGQ9Ik0xMCAzNUMxNSAzMCAyMCAzNSAyNSAzMEMzMCAzNSAzNSAzMCA0MCAzNSIgc3Ryb2tlPSJyZ2JhKDE4NCwxMzQsMTEsLjEpIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9Im5vbmUiLz4KICAgIDwvcGF0dGVybj4KICA8L2RlZnM+CiAgPHJlY3Qgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgZmlsbD0idXJsKCNtYW5nb0xlYWYpIi8+Cjwvc3ZnPg==',
    borderStyle: 'double',
    decorativeElements: ['🕉️', '🌸', '🪔']
  },
  {
    id: 'royal',
    name: 'Royal Maroon (शाही मरून)',
    colors: {
      primary: '#800020', // Deep maroon for royalty
      secondary: '#DC143C', // Crimson for richness
      accent: '#FFD700', // Gold for traditional accents
      text: '#FFFFFF' // White for contrast on dark backgrounds
    },
    pattern: 'linear-gradient(135deg, #DC143C 0%, #800020 50%, #4A0E0E 100%)',
    backgroundImage: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxwYXR0ZXJuIGlkPSJwYWlzbGV5IiBwYXR0ZXJuVW5pdHM9InVzZXJTcGFjZU9uVXNlIiB3aWR0aD0iODAiIGhlaWdodD0iODAiPgogICAgICA8IS0tIFRyYWRpdGlvbmFsIFBhaXNsZXkgTW90aWYgLS0+CiAgICAgIDxwYXRoIGQ9Ik00MCA1QzU1IDEwIDY1IDI1IDYwIDQwQzU1IDU1IDQwIDY1IDI1IDYwQzEwIDU1IDUgNDAgMTAgMjVDMTUgMTAgMzAgNSA0MCA1WiIgZmlsbD0icmdiYSgyNTUsMjE1LDQwLC4xMikiIHN0cm9rZT0icmdiYSgyNTUsMjE1LDQwLC4wOCkiIHN0cm9rZS13aWR0aD0iMSIvPgogICAgICA8IS0tIElubmVyIGRldGFpbHMgLS0+CiAgICAgIDxwYXRoIGQ9Ik00MCAyMEM0NSAyNSA0NSAzNSA0MCA0MEM0MCA0NSAzNSA0NSAzMCA0MEM0MCAzNSA0MCAyNSA0MCAyMFoiIGZpbGw9InJnYmEoMjU1LDIxNSw0MCwuMTUpIi8+CiAgICAgIDxjaXJjbGUgY3g9IjQwIiBjeT0iMzAiIHI9IjMiIGZpbGw9InJnYmEoMjU1LDIxNSw0MCwuMikiLz4KICAgICAgPCEtLSBEZWNvcmF0aXZlIGRvdHMgLS0+CiAgICAgIDxjaXJjbGUgY3g9IjIwIiBjeT0iMjAiIHI9IjIiIGZpbGw9InJnYmEoMjU1LDIxNSw0MCwuMSkiLz4KICAgICAgPGNpcmNsZSBjeD0iNjAiIGN5PSI2MCIgcj0iMiIgZmlsbD0icmdiYSgyNTUsMjE1LDQwLC4xKSIvPgogICAgPC9wYXR0ZXJuPgogIDwvZGVmcz4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSJ1cmwoI3BhaXNsZXkpIi8+Cjwvc3ZnPg==',
    borderStyle: 'ridge',
    decorativeElements: ['👑', '💎', '🌹']
  },
  {
    id: 'elegant',
    name: 'Elegant Saffron (नाजूक केशरी)',
    colors: {
      primary: '#FF8C00', // Saffron for auspiciousness
      secondary: '#FFA500', // Orange for warmth
      accent: '#8B4513', // Brown for grounding
      text: '#333333' // Dark gray for readability
    },
    pattern: 'linear-gradient(135deg, #FFA500 0%, #FF8C00 50%, #CC6600 100%)',
    backgroundImage: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwIiBoZWlnaHQ9IjEyMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxwYXR0ZXJuIGlkPSJsb3R1cyIgcGF0dGVyblVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgd2lkdGg9IjEyMCIgaGVpZ2h0PSIxMjAiPgogICAgICA8IS0tIExvdHVzIE1hbmRhbGEgLS0+CiAgICAgIDxnIHRyYW5zZm9ybT0idHJhbnNsYXRlKDYwLDYwKSI+CiAgICAgICAgPCEtLSBPdXRlciBwZXRhbHMgLS0+CiAgICAgICAgPGcgc3Ryb2tlPSJyZ2JhKDEzOSwzMywxOSwuMTIpIiBzdHJva2Utd2lkdGg9IjEiIGZpbGw9InJnYmEoMTM5LDMzLDE5LC4wOCkiPgogICAgICAgICAgPGVsbGlwc2UgY3g9IjAiIGN5PSItMzAiIHJ4PSI4IiByeT0iMjAiIHRyYW5zZm9ybT0icm90YXRlKDApIi8+CiAgICAgICAgICA8ZWxsaXBzZSBjeD0iMCIgY3k9Ii0zMCIgcng9IjgiIHJ5PSIyMCIgdHJhbnNmb3JtPSJyb3RhdGUoNDUpIi8+CiAgICAgICAgICA8ZWxsaXBzZSBjeD0iMCIgY3k9Ii0zMCIgcng9IjgiIHJ5PSIyMCIgdHJhbnNmb3JtPSJyb3RhdGUoOTApIi8+CiAgICAgICAgICA8ZWxsaXBzZSBjeD0iMCIgY3k9Ii0zMCIgcng9IjgiIHJ5PSIyMCIgdHJhbnNmb3JtPSJyb3RhdGUoMTM1KSIvPgogICAgICAgICAgPGVsbGlwc2UgY3g9IjAiIGN5PSItMzAiIHJ4PSI4IiByeT0iMjAiIHRyYW5zZm9ybT0icm90YXRlKDE4MCkiLz4KICAgICAgICAgIDxlbGxpcHNlIGN4PSIwIiBjeT0iLTMwIiByeD0iOCIgcnk9IjIwIiB0cmFuc2Zvcm09InJvdGF0ZSgyMjUpIi8+CiAgICAgICAgICA8ZWxsaXBzZSBjeD0iMCIgY3k9Ii0zMCIgcng9IjgiIHJ5PSIyMCIgdHJhbnNmb3JtPSJyb3RhdGUoMjcwKSIvPgogICAgICAgICAgPGVsbGlwc2UgY3g9IjAiIGN5PSItMzAiIHJ4PSI4IiByeT0iMjAiIHRyYW5zZm9ybT0icm90YXRlKDMxNSkiLz4KICAgICAgICA8L2c+CiAgICAgICAgPCEtLSBJbm5lciBjaXJjbGUgLS0+CiAgICAgICAgPGNpcmNsZSBjeD0iMCIgY3k9IjAiIHI9IjEyIiBmaWxsPSJyZ2JhKDEzOSwzMywxOSwuMTUpIiBzdHJva2U9InJnYmEoMTM5LDMzLDE5LC4yKSIgc3Ryb2tlLXdpZHRoPSIxIi8+CiAgICAgICAgPGNpcmNsZSBjeD0iMCIgY3k9IjAiIHI9IjQiIGZpbGw9InJnYmEoMTM5LDMzLDE5LC4yNSkiLz4KICAgICAgPC9nPgogICAgPC9wYXR0ZXJuPgogIDwvZGVmcz4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSJ1cmwoI2xvdHVzKSIvPgo8L3N2Zz4=',
    borderStyle: 'solid',
    decorativeElements: ['🪷', '🕉️', '✨']
  },
  {
    id: 'modern',
    name: 'Rangoli Pink (रांगोळी गुलाबी)',
    colors: {
      primary: '#E91E63', // Vibrant pink for modern appeal
      secondary: '#F8BBD9', // Light pink for softness
      accent: '#AD1457', // Deep pink for depth
      text: '#2C2C2C' // Dark gray for text clarity
    },
    pattern: 'linear-gradient(135deg, #F8BBD9 0%, #E91E63 50%, #AD1457 100%)',
    backgroundImage: 'data:image/svg+xml;base64,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',
    borderStyle: 'dotted',
    decorativeElements: ['🌺', '💖', '🦋']
  },
  {
    id: 'peacock',
    name: 'Peacock Green (मोरपंखी हिरवा)',
    colors: {
      primary: '#006400', // Deep green inspired by peacock feathers
      secondary: '#20B2AA', // Teal for vibrancy
      accent: '#FFD700', // Gold for accents
      text: '#FFFFFF' // White for contrast
    },
    pattern: 'linear-gradient(135deg, #20B2AA 0%, #006400 50%, #003300 100%)',
    backgroundImage: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxwYXR0ZXJuIGlkPSJwZWFjb2NrIiBwYXR0ZXJuVW5pdHM9InVzZXJTcGFjZU9uVXNlIiB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCI+CiAgICAgIDwhLS0gUGVhY29jayBGZWF0aGVyIEV5ZSAtLT4KICAgICAgPGcgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoNTAsNTApIj4KICAgICAgICA8IS0tIE91dGVyIGV5ZSAtLT4KICAgICAgICA8ZWxsaXBzZSBjeD0iMCIgY3k9IjAiIHJ4PSIyNSIgcnk9IjM1IiBmaWxsPSJyZ2JhKDMyLDE3OCwxNzAsLjEpIiBzdHJva2U9InJnYmEoMjU1LDIxNSw0MCwuMTUpIiBzdHJva2Utd2lkdGg9IjIiLz4KICAgICAgICA8IS0tIE1pZGRsZSBleWUgLS0+CiAgICAgICAgPGVsbGlwc2UgY3g9IjAiIGN5PSIwIiByeD0iMTUiIHJ5PSIyMCIgZmlsbD0icmdiYSgwLDEwMCwwLC4xMikiLz4KICAgICAgICA8IS0tIElubmVyIGV5ZSAtLT4KICAgICAgICA8Y2lyY2xlIGN4PSIwIiBjeT0iMCIgcj0iOCIgZmlsbD0icmdiYSgyNTUsMjE1LDQwLC4yKSIvPgogICAgICAgIDxjaXJjbGUgY3g9IjAiIGN5PSIwIiByPSI0IiBmaWxsPSJyZ2JhKDAsMTAwLDAsLjMpIi8+CiAgICAgICAgPCEtLSBGZWF0aGVyIGJhcmJzIC0tPgogICAgICAgIDxwYXRoIGQ9Ik0tMzAgLTQwTDMwIC00ME0tMjUgLTMwTDI1IC0zME0tMjAgLTIwTDIwIC0yME0tMTUgLTEwTDE1IC0xME0tMTAgMEwxMCAwTTEwIDEwTC0xMCAxME0xNSAyMEwtMTUgMjBNMjAgMzBMLTIwIDMwTTI1IDQwTC0yNSA0MCIgc3Ryb2tlPSJyZ2JhKDMyLDE3OCwxNzAsLjA4KSIgc3Ryb2tlLXdpZHRoPSIxIi8+CiAgICAgIDwvZz4KICAgIDwvcGF0dGVybj4KICA8L2RlZnM+CiAgPHJlY3Qgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgZmlsbD0idXJsKCNwZWFjb2NrKSIvPgo8L3N2Zz4=',
    borderStyle: 'groove',
    decorativeElements: ['🦚', '💚', '🌿']
  },
  {
    id: 'temple',
    name: 'Temple Serenity (मंदिर शांतता)',
    colors: {
      primary: '#8B008B', // Deep purple for spirituality
      secondary: '#DDA0DD', // Light purple for calm
      accent: '#FFD700', // Gold for sacred elements
      text: '#2C2C2C' // Dark gray for readability
    },
    pattern: 'linear-gradient(135deg, #DDA0DD 0%, #8B008B 50%, #4B004B 100%)',
    backgroundImage: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxwYXR0ZXJuIGlkPSJ0ZW1wbGUiIHBhdHRlcm5Vbml0cz0idXNlclNwYWNlT25Vc2UiIHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIj4KICAgICAgPCEtLSBUZW1wbGUgR2VvbWV0cmljIFBhdHRlcm4gLS0+CiAgICAgIDxnIHRyYW5zZm9ybT0idHJhbnNsYXRlKDUwLDUwKSI+CiAgICAgICAgPCEtLSBDZW50cmFsIHNxdWFyZSAtLT4KICAgICAgICA8cmVjdCB4PSItMTUiIHk9Ii0xNSIgd2lkdGg9IjMwIiBoZWlnaHQ9IjMwIiBmaWxsPSJyZ2JhKDI1NSwyMTUsNDAsLjEyKSIgc3Ryb2tlPSJyZ2JhKDI1NSwyMTUsNDAsLjE1KSIgc3Ryb2tlLXdpZHRoPSIxIi8+CiAgICAgICAgPCEtLSBJbm5lciBkaWFtb25kIC0tPgogICAgICAgIDxwYXRoIGQ9Ik0wIC0yMEwyMCAwTDAgMjBMLTIwIDBaIiBmaWxsPSJyZ2JhKDI1NSwyMTUsNDAsLjA4KSIgc3Ryb2tlPSJyZ2JhKDI1NSwyMTUsNDAsLjEpIiBzdHJva2Utd2lkdGg9IjEiLz4KICAgICAgICA8IS0tIENlbnRyYWwgY2lyY2xlIC0tPgogICAgICAgIDxjaXJjbGUgY3g9IjAiIGN5PSIwIiByPSI4IiBmaWxsPSJyZ2JhKDI1NSwyMTUsNDAsLjE1KSIgc3Ryb2tlPSJyZ2JhKDI1NSwyMTUsNDAsLjIpIiBzdHJva2Utd2lkdGg9IjEiLz4KICAgICAgICA8IS0tIENvcm5lciBkb3RzIC0tPgogICAgICAgIDxjaXJjbGUgY3g9Ii0zNSIgY3k9Ii0zNSIgcj0iMyIgZmlsbD0icmdiYSgyNTUsMjE1LDQwLC4xKSIvPgogICAgICAgIDxjaXJjbGUgY3g9IjM1IiBjeT0iLTM1IiByPSIzIiBmaWxsPSJyZ2JhKDI1NSwyMTUsNDAsLjEpIi8+CiAgICAgICAgPGNpcmNsZSBjeD0iLTM1IiBjeT0iMzUiIHI9IjMiIGZpbGw9InJnYmEoMjU1LDIxNSw0MCwuMSkiLz4KICAgICAgICA8Y2lyY2xlIGN4PSIzNSIgY3k9IjM1IiByPSIzIiBmaWxsPSJyZ2JhKDI1NSwyMTUsNDAsLjEpIi8+CiAgICAgIDwvZz4KICAgIDwvcGF0dGVybj4KICA8L2RlZnM+CiAgPHJlY3Qgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgZmlsbD0idXJsKCN0ZW1wbGUpIi8+Cjwvc3ZnPg==',
    borderStyle: 'inset',
    decorativeElements: ['🛕', '🕉️', '🙏']
  },
  {
    id: 'floral',
    name: 'Floral Bliss (फुलांचा आनंद)',
    colors: {
      primary: '#228B22', // Forest green for nature
      secondary: '#98FB98', // Pale green for freshness
      accent: '#FF69B4', // Hot pink for floral accents
      text: '#333333' // Dark gray for text
    },
    pattern: 'linear-gradient(135deg, #98FB98 0%, #228B22 50%, #006400 100%)',
    backgroundImage: 'data:image/svg+xml;base64,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',
    borderStyle: 'dashed',
    decorativeElements: ['🌼', '🌻', '🌺']
  },
  {
    id: 'classic',
    name: 'Classic Cream (क्लासिक क्रीम)',
    colors: {
      primary: '#FFF8DC', // Cream for elegance
      secondary: '#F5DEB3', // Wheat for warmth
      accent: '#8B4513', // Saffron brown for contrast
      text: '#2C2C2C' // Dark gray for readability
    },
    pattern: 'linear-gradient(135deg, #FFF8DC 0%, #F5DEB3 50%, #DEB887 100%)',
    backgroundImage: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxwYXR0ZXJuIGlkPSJ0cmFkaXRpb25hbCIgcGF0dGVyblVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgd2lkdGg9IjEwMCIgaGVpZ2h0PSIxMDAiPgogICAgICA8IS0tIFRyYWRpdGlvbmFsIEJvcmRlciBQYXR0ZXJuIC0tPgogICAgICA8ZyB0cmFuc2Zvcm09InRyYW5zbGF0ZSg1MCw1MCkiPgogICAgICAgIDwhLS0gQ2VudHJhbCBtb3RpZiAtLT4KICAgICAgICA8cG9seWdvbiBwb2ludHM9IjAuLTIwIDIwLDAgMCwyMCAtMjAsMCIgZmlsbD0icmdiYSgxMzksMzMsMTksLjEpIiBzdHJva2U9InJnYmEoMTM5LDMzLDE5LC4xNSkiIHN0cm9rZS13aWR0aD0iMSIvPgogICAgICAgIDwhLS0gSW5uZXIgc3F1YXJlIC0tPgogICAgICAgIDxyZWN0IHg9Ii0xMCIgeT0iLTEwIiB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIGZpbGw9InJnYmEoMTM5LDMzLDE5LC4wOCkiIHN0cm9rZT0icmdiYSgxMzksMzMsMTksLjEyKSIgc3Ryb2tlLXdpZHRoPSIxIi8+CiAgICAgICAgPCEtLSBDZW50ZXIgZG90IC0tPgogICAgICAgIDxjaXJjbGUgY3g9IjAiIGN5PSIwIiByPSI0IiBmaWxsPSJyZ2JhKDEzOSwzMywxOSwuMTUpIi8+CiAgICAgICAgPCEtLSBDb3JuZXIgZGVjb3JhdGlvbnMgLS0+CiAgICAgICAgPGNpcmNsZSBjeD0iLTM1IiBjeT0iLTM1IiByPSIzIiBmaWxsPSJyZ2JhKDEzOSwzMywxOSwuMSkiLz4KICAgICAgICA8Y2lyY2xlIGN4PSIzNSIgY3k9Ii0zNSIgcj0iMyIgZmlsbD0icmdiYSgxMzksMzMsMTksLjEpIi8+CiAgICAgICAgPGNpcmNsZSBjeD0iLTM1IiBjeT0iMzUiIHI9IjMiIGZpbGw9InJnYmEoMTM5LDMzLDE5LC4xKSIvPgogICAgICAgIDxjaXJjbGUgY3g9IjM1IiBjeT0iMzUiIHI9IjMiIGZpbGw9InJnYmEoMTM5LDMzLDE5LC4xKSIvPgogICAgICAgIDwhLS0gRGVjb3JhdGl2ZSBsaW5lcyAtLT4KICAgICAgICA8cGF0aCBkPSJNLTQwIC00ME00MCA0ME0tNDAgNDBMNDAgLTQwIiBzdHJva2U9InJnYmEoMTM5LDMzLDE5LC4wNikiIHN0cm9rZS13aWR0aD0iMSIvPgogICAgICA8L2c+CiAgICA8L3BhdHRlcm4+CiAgPC9kZWZzPgogIDxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InVybCgjdHJhZGl0aW9uYWwpIi8+Cjwvc3ZnPg==',
    borderStyle: 'solid',
    decorativeElements: ['🕉️', '📿', '🪔']
  },
  {
    id: 'festival',
    name: 'Festival Diwali (दिवाळी उत्सव)',
    colors: {
      primary: '#FF6B35', // Vibrant orange for celebration
      secondary: '#FFD23F', // Bright yellow for joy
      accent: '#8B0000', // Deep red for auspiciousness
      text: '#2C2C2C' // Dark gray for readability
    },
    pattern: 'linear-gradient(135deg, #FFD23F 0%, #FF6B35 50%, #8B0000 100%)',
    backgroundImage: 'data:image/svg+xml;base64,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',
    borderStyle: 'double',
    decorativeElements: ['🪔', '✨', '🎆']
  }
];

const ceremonyTypes = [
  {
    value: 'विवाह समारंभ',
    label: 'विवाह समारंभ (Wedding)',
    icon: Heart,
    fields: [
      'brideName', // वधूचे नाव (Bride's name)
      'groomName', // वराचे नाव (Groom's name)
      'fatherName', // वधूचे वडील (Bride's father)
      'motherName', // वधूची आई (Bride's mother)
      'groomFatherName', // वराचे वडील (Groom's father)
      'groomMotherName', // वराची आई (Groom's mother)
      'date', // तारीख (Date)
      'time', // वेळ (Time)
      'venue', // स्थळ (Venue)
      'address', // पत्ता (Address)
      'hostFamily' // यजमान कुटुंब (Host family)
    ]
  },
  {
    value: 'साखरपुडा',
    label: 'साखरपुडा (Engagement)',
    icon: Gift,
    fields: [
      'brideName', // वधूचे नाव
      'groomName', // वराचे नाव
      'fatherName', // वधूचे वडील
      'motherName', // वधूची आई
      'groomFatherName', // वराचे वडील
      'groomMotherName', // वराची आई
      'date', // तारीख
      'time', // वेळ
      'venue', // स्थळ
      'address', // पत्ता
      'hostFamily' // यजमान कुटुंब
    ]
  },
  {
    value: 'केल्वण',
    label: 'केल्वण (Kelvan / Pre-wedding Feast)',
    icon: Users,
    fields: [
      'hostFamily', // यजमान कुटुंब
      'date', // तारीख
      'time', // वेळ
      'venue', // स्थळ
      'address', // पत्ता
      'additionalInfo' // अतिरिक्त माहिती (Additional details)
    ]
  },
  {
    value: 'हळदी समारंभ',
    label: 'हळदी समारंभ (Haldi Ceremony)',
    icon: Sparkles,
    fields: [
      'brideName', // वधूचे नाव
      'groomName', // वराचे नाव
      'date', // तारीख
      'time', // वेळ
      'venue', // स्थळ
      'address', // पत्ता
      'hostFamily' // यजमान कुटुंब
    ]
  },
  {
    value: 'गौरीहार पूजा',
    label: 'गौरीहार पूजा (Gaurihar Puja)',
    icon: Calendar,
    fields: [
      'brideName', // वधूचे नाव
      'date', // तारीख
      'time', // वेळ
      'venue', // स्थळ
      'address', // पत्ता
      'hostFamily' // यजमान कुटुंब
    ]
  },
  {
    value: 'अंतरपट',
    label: 'अंतरपट (Antarpat Ritual)',
    icon: Calendar,
    fields: [
      'brideName', // वधूचे नाव
      'groomName', // वराचे नाव
      'date', // तारीख
      'time', // वेळ
      'venue', // स्थळ
      'address' // पत्ता
    ]
  },
  {
    value: 'संकल्प',
    label: 'संकल्प (Sankalp / Resolution Ceremony)',
    icon: Calendar,
    fields: [
      'hostFamily', // यजमान कुटुंब
      'date', // तारीख
      'time', // वेळ
      'venue', // स्थळ
      'address', // पत्ता
      'additionalInfo' // अतिरिक्त माहिती
    ]
  },
  {
    value: 'कन्यादान आणि सप्तपदी',
    label: 'कन्यादान आणि सप्तपदी (Kanyadaan & Saptapadi)',
    icon: Heart,
    fields: [
      'brideName', // वधूचे नाव
      'groomName', // वराचे नाव
      'fatherName', // वधूचे वडील
      'motherName', // वधूची आई
      'groomFatherName', // वराचे वडील
      'groomMotherName', // वराची आई
      'date', // तारीख
      'time', // वेळ
      'venue', // स्थळ
      'address', // पत्ता
      'hostFamily' // यजमान कुटुंब
    ]
  },
  {
    value: 'कर्मसमाप्ती',
    label: 'कर्मसमाप्ती (Karmasamapti / Concluding Ritual)',
    icon: Sparkles,
    fields: [
      'brideName', // वधूचे नाव
      'groomName', // वराचे नाव
      'date', // तारीख
      'time', // वेळ
      'venue', // स्थळ
      'address', // पत्ता
      'hostFamily' // यजमान कुटुंब
    ]
  },
  {
    value: 'वरात आणि गृहप्रवेश',
    label: 'वरात आणि गृहप्रवेश (Baraat & Grihapravesh)',
    icon: MapPin,
    fields: [
      'brideName', // वधूचे नाव
      'groomName', // वराचे नाव
      'hostFamily', // यजमान कुटुंब
      'date', // तारीख
      'time', // वेळ
      'venue', // स्थळ
      'address' // पत्ता
    ]
  },
  {
    value: 'रिसेप्शन',
    label: 'रिसेप्शन (Reception)',
    icon: Users,
    fields: [
      'brideName', // वधूचे नाव
      'groomName', // वराचे नाव
      'date', // तारीख
      'time', // वेळ
      'venue', // स्थळ
      'address', // पत्ता
      'hostFamily' // यजमान कुटुंब
    ]
  },
  {
    value: 'नामकरण सोहळा',
    label: 'नामकरण सोहळा (Naming Ceremony)',
    icon: Sparkles,
    fields: [
      'childName', // मुलाचे/मुलीचे नाव (Child's name)
      'fatherName', // वडील (Father's name)
      'motherName', // आई (Mother's name)
      'date', // तारीख
      'time', // वेळ
      'venue', // स्थळ
      'address', // पत्ता
      'hostFamily' // यजमान कुटुंब
    ]
  },
  {
    value: 'डोहाळे जेवण',
    label: 'डोहाळे जेवण (Baby Shower)',
    icon: Gift,
    fields: [
      'motherName', // आईचे नाव (Mother's name)
      'fatherName', // वडिलांचे नाव (Father's name)
      'date', // तारीख
      'time', // वेळ
      'venue', // स्थळ
      'address', // पत्ता
      'hostFamily' // यजमान कुटुंब
    ]
  },
  {
    value: 'वास्तुशांती',
    label: 'वास्तुशांती (Housewarming)',
    icon: MapPin,
    fields: [
      'hostFamily', // यजमान कुटुंब
      'date', // तारीख
      'time', // वेळ
      'venue', // स्थळ
      'address', // पत्ता
      'additionalInfo' // अतिरिक्त माहिती
    ]
  },
  {
    value: 'बारसे',
    label: 'बारसे (12th Day Naming Ceremony)',
    icon: Sparkles,
    fields: [
      'childName', // मुलाचे/मुलीचे नाव
      'fatherName', // वडील
      'motherName', // आई
      'date', // तारीख
      'time', // वेळ
      'venue', // स्थळ
      'address', // पत्ता
      'hostFamily' // यजमान कुटुंब
    ]
  },
  {
    value: 'मुंज',
    label: 'मुंज (Thread Ceremony / Upanayan)',
    icon: Calendar,
    fields: [
      'childName', // मुलाचे नाव (Boy's name)
      'fatherName', // वडील
      'motherName', // आई
      'date', // तारीख
      'time', // वेळ
      'venue', // स्थळ
      'address', // पत्ता
      'hostFamily' // यजमान कुटुंब
    ]
  },
  {
    value: 'सत्यनारायण पूजा',
    label: 'सत्यनारायण पूजा (Satyanarayan Puja)',
    icon: Calendar,
    fields: [
      'hostFamily', // यजमान कुटुंब
      'date', // तारीख
      'time', // वेळ
      'venue', // स्थळ
      'address', // पत्ता
      'additionalInfo' // अतिरिक्त माहिती
    ]
  },
  {
    value: 'सगाई',
    label: 'सगाई (Engagement / Ring Ceremony)',
    icon: Gift,
    fields: [
      'brideName', // वधूचे नाव
      'groomName', // वराचे नाव
      'fatherName', // वधूचे वडील
      'motherName', // वधूची आई
      'groomFatherName', // वराचे वडील
      'groomMotherName', // वराची आई
      'date', // तारीख
      'time', // वेळ
      'venue', // स्थळ
      'address', // पत्ता
      'hostFamily' // यजमान कुटुंब
    ]
  },
  {
    value: 'अन्नप्राशन',
    label: 'अन्नप्राशन (First Feeding Ceremony)',
    icon: Sparkles,
    fields: [
      'childName', // मुलाचे/मुलीचे नाव
      'fatherName', // वडील
      'motherName', // आई
      'date', // तारीख
      'time', // वेळ
      'venue', // स्थळ
      'address', // पत्ता
      'hostFamily' // यजमान कुटुंब
    ]
  },
  {
    value: 'लग्नाचा वाढदिवस',
    label: 'लग्नाचा वाढदिवस (Wedding Anniversary)',
    icon: Heart,
    fields: [
      'husbandName', // पतीचे नाव (Husband's name)
      'wifeName', // पत्नीचे नाव (Wife's name)
      'date', // तारीख
      'time', // वेळ
      'venue', // स्थळ
      'address', // पत्ता
      'hostFamily' // यजमान कुटुंब
    ]
  }
];

const ganeshaImages = [
  {
    id: 'ganesha1',
    name: 'Traditional Ganesha',
    url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxkZWZzPgo8cmFkaWFsR3JhZGllbnQgaWQ9InJhZGlhbDEiIGN4PSI1MCUiIGN5PSI1MCUiIHI9IjUwJSI+CjxzdG9wIG9mZnNldD0iMCUiIHN0eWxlPSJzdG9wLWNvbG9yOiNGRkQ3MDA7c3RvcC1vcGFjaXR5OjEiIC8+CjxzdG9wIG9mZnNldD0iNzAlIiBzdHlsZT0ic3RvcC1jb2xvcjojRkY5NTAwO3N0b3Atb3BhY2l0eToxIiAvPgo8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0eWxlPSJzdG9wLWNvbG9yOiNEQzE0M0M7c3RvcC1vcGFjaXR5OjEiIC8+CjwvcmFkaWFsR3JhZGllbnQ+CjwvZGVmcz4KPGNpcmNsZSBjeD0iMTAwIiBjeT0iMTAwIiByPSI4NSIgZmlsbD0idXJsKCNyYWRpYWwxKSIgc3Ryb2tlPSIjRkZENzAwIiBzdHJva2Utd2lkdGg9IjMiLz4KPGNpcmNsZSBjeD0iMTAwIiBjeT0iMTAwIiByPSI2NSIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjRkZENzAwIiBzdHJva2Utd2lkdGg9IjIiLz4KPGNpcmNsZSBjeD0iMTAwIiBjeT0iMTAwIiByPSI0NSIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjRkZENzAwIiBzdHJva2Utd2lkdGg9IjEuNSIvPgo8dGV4dCB4PSIxMDAiIHk9IjExNSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZmlsbD0id2hpdGUiIGZvbnQtc2l6ZT0iMzYiIGZvbnQtd2VpZ2h0PSJib2xkIiBmb250LWZhbWlseT0ic2VyaWYiPuCklzwvdGV4dD4KPHBhdGggZD0iTTcwIDcwTDEzMCA3MEwxMzAgMTMwTDcwIDEzMFoiIGZpbGw9Im5vbmUiIHN0cm9rZT0iI0ZGRDcwMCIgc3Ryb2tlLXdpZHRoPSIxIiBzdHJva2UtZGFzaGFycmF5PSI1LDUiLz4KPC9zdmc+'
  },
  {
    id: 'ganesha2',
    name: 'Colorful Ganesha',
    url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxkZWZzPgo8bGluZWFyR3JhZGllbnQgaWQ9ImdyYWQxIiB4MT0iMCUiIHkxPSIwJSIgeDI9IjEwMCUiIHkyPSIxMDAlIj4KPHN0b3Agb2Zmc2V0PSIwJSIgc3R5bGU9InN0b3AtY29sb3I6I0ZGNjUwMDtzdG9wLW9wYWNpdHk6MSIgLz4KPHN0b3Agb2Zmc2V0PSI1MCUiIHN0eWxlPSJzdG9wLWNvbG9yOiNGRkQ3MDA7c3RvcC1vcGFjaXR5OjEiIC8+CjxzdG9wIG9mZnNldD0iMTAwJSIgc3R5bGU9InN0b3AtY29sb3I6I0RDMTQzQztzdG9wLW9wYWNpdHk6MSIgLz4KPC9saW5lYXJHcmFkaWVudD4KPC9kZWZzPgo8cGF0aCBkPSJNMTAwIDIwTDEyMCA0MEwxNDAgNjBMMTYwIDgwTDE4MCAxMDBMMTYwIDEyMEwxNDAgMTQwTDEyMCAxNjBMMTAwIDE4MEw4MCAxNjBMNjAgMTQwTDQwIDEyMEwyMCAxMDBMNDAgODBMNjAgNjBMODAgNDBMMTAwIDIwWiIgZmlsbD0idXJsKCNncmFkMSkiIHN0cm9rZT0iI0ZGRDcwMCIgc3Ryb2tlLXdpZHRoPSIzIi8+CjxjaXJjbGUgY3g9IjEwMCIgY3k9IjEwMCIgcj0iNDAiIGZpbGw9Im5vbmUiIHN0cm9rZT0iI0ZGRDcwMCIgc3Ryb2tlLXdpZHRoPSIyIi8+Cjx0ZXh0IHg9IjEwMCIgeT0iMTE1IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSJ3aGl0ZSIgZm9udC1zaXplPSIzMiIgZm9udC13ZWlnaHQ9ImJvbGQiIGZvbnQtZmFtaWx5PSJzZXJpZiI+4KSXPC90ZXh0Pgo8dGV4dCB4PSIxMDAiIHk9IjE0NSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZmlsbD0iI0ZGRDcwMCIgZm9udC1zaXplPSIxNiIgZm9udC13ZWlnaHQ9ImJvbGQiPuCkl+Cko+ClheCktuCkpzwvdGV4dD4KPC9zdmc+'
  },
  {
    id: 'ganesha3',
    name: 'Simple Ganesha',
    url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxjaXJjbGUgY3g9IjEwMCIgY3k9IjEwMCIgcj0iNzUiIGZpbGw9Im5vbmUiIHN0cm9rZT0iIzY2MzM5OSIgc3Ryb2tlLXdpZHRoPSI0Ii8+CjxjaXJjbGUgY3g9IjEwMCIgY3k9IjEwMCIgcj0iNTUiIGZpbGw9Im5vbmUiIHN0cm9rZT0iIzY2MzM5OSIgc3Ryb2tlLXdpZHRoPSIzIi8+CjxjaXJjbGUgY3g9IjEwMCIgY3k9IjEwMCIgcj0iMzUiIGZpbGw9Im5vbmUiIHN0cm9rZT0iIzY2MzM5OSIgc3Ryb2tlLXdpZHRoPSIyIi8+Cjx0ZXh0IHg9IjEwMCIgeT0iMTE1IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNjYzMzk5IiBmb250LXNpemU9IjM0IiBmb250LXdlaWdodD0iYm9sZCIgZm9udC1mYW1pbHk9InNlcmlmIj7gpJc8L3RleHQ+CjxwYXRoIGQ9Ik04MCA4MEwxMjAgODBMMTIwIDEyMEw4MCAxMjBaIiBmaWxsPSJub25lIiBzdHJva2U9IiM2NjMzOTkiIHN0cm9rZS13aWR0aD0iMS41IiBzdHJva2UtZGFzaGFycmF5PSI0LDQiLz4KPC9zdmc+'
  },
  {
    id: 'ganesha4',
    name: 'Decorative Ganesha',
    url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxkZWZzPgo8cmFkaWFsR3JhZGllbnQgaWQ9InJhZGlhbDEiIGN4PSI1MCUiIGN5PSI1MCUiIHI9IjUwJSI+CjxzdG9wIG9mZnNldD0iMCUiIHN0eWxlPSJzdG9wLWNvbG9yOiNGRkQ3MDA7c3RvcC1vcGFjaXR5OjEiIC8+CjxzdG9wIG9mZnNldD0iNzAlIiBzdHlsZT0ic3RvcC1jb2xvcjojRkY5NTAwO3N0b3Atb3BhY2l0eToxIiAvPgo8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0eWxlPSJzdG9wLWNvbG9yOiNEQzE0M0M7c3RvcC1vcGFjaXR5OjEiIC8+CjwvcmFkaWFsR3JhZGllbnQ+CjwvZGVmcz4KPHBhdGggZD0iTTEwMCAyMEw4NSAzNUw3MCA1MEw1NSA2NUw0MCA4MEw1NSA5NUw3MCAxMTBMODUgMTI1TDEwMCAxNDBMMTE1IDEyNUwxMzAgMTEwTDE0NSA5NUwxNjAgODBMMTQ1IDY1TDEzMCA1MEwxMTUgMzVMMTAwIDIwWiIgZmlsbD0idXJsKCNyYWRpYWwxKSIgc3Ryb2tlPSIjRkZENzAwIiBzdHJva2Utd2lkdGg9IjMiLz4KPGNpcmNsZSBjeD0iMTAwIiBjeT0iMTAwIiByPSI0NSIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjRkZENzAwIiBzdHJva2Utd2lkdGg9IjIuNSIvPgo8Y2lyY2xlIGN4PSIxMDAiIGN5PSIxMDAiIHI9IjI1IiBmaWxsPSJub25lIiBzdHJva2U9IiNGRkQ3MDAiIHN0cm9rZS13aWR0aD0iMS41Ii8+Cjx0ZXh0IHg9IjEwMCIgeT0iMTE1IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSJ3aGl0ZSIgZm9udC1zaXplPSIzMCIgZm9udC13ZWlnaHQ9ImJvbGQiIGZvbnQtZmFtaWx5PSJzZXJpZiI+4KSXPC90ZXh0Pgo8dGV4dCB4PSIxMDAiIHk9IjE2NSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZmlsbD0iI0ZGRDcwMCIgZm9udC1zaXplPSIxNCIgZm9udC13ZWlnaHQ9ImJvbGQiPuCkl+Cko+ClheCktuCkpzwvdGV4dD4KPC9zdmc+'
  },
  {
    id: 'ganesha5',
    name: 'Elegant Ganesha',
    url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxkZWZzPgo8bGluZWFyR3JhZGllbnQgaWQ9ImVsZWdhbnQiIHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjEwMCUiPgo8c3RvcCBvZmZzZXQ9IjAlIiBzdHlsZT0ic3RvcC1jb2xvcjojOEI0NTEzO3N0b3Atb3BhY2l0eToxIiAvPgo8c3RvcCBvZmZzZXQ9IjUwJSIgc3R5bGU9InN0b3AtY29sb3I6I0NEODUzRjtzdG9wLW9wYWNpdHk6MSIgLz4KPHN0b3Agb2Zmc2V0PSIxMDAlIiBzdHlsZT0ic3RvcC1jb2xvcjojRkZEQjU4O3N0b3Atb3BhY2l0eToxIiAvPgo8L2xpbmVhckdyYWRpZW50Pgo8L2RlZnM+CjxlbGxpcHNlIGN4PSIxMDAiIGN5PSIxMDAiIHJ4PSI3NSIgcnk9Ijg1IiBmaWxsPSJ1cmwoI2VsZWdhbnQpIiBzdHJva2U9IiNGRkRCNTgiIHN0cm9rZS13aWR0aD0iMyIvPgo8ZWxsaXBzZSBjeD0iMTAwIiBjeT0iMTAwIiByeD0iNTUiIHJ5PSI2NSIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjRkZEQjU4IiBzdHJva2Utd2lkdGg9IjIuNSIvPgo8ZWxsaXBzZSBjeD0iMTAwIiBjeT0iMTAwIiByeD0iMzUiIHJ5PSI0NSIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjRkZEQjU4IiBzdHJva2Utd2lkdGg9IjIiLz4KPHRleHQgeD0iMTAwIiB5PSIxMTUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGZpbGw9IiNGRkRCNTgiIGZvbnQtc2l6ZT0iMzAiIGZvbnQtd2VpZ2h0PSJib2xkIiBmb250LWZhbWlseT0ic2VyaWYiPuCklzwvdGV4dD4KPHRleHQgeD0iMTAwIiB5PSIxNTUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGZpbGw9IiNGRkRCNTgiIGZvbnQtc2l6ZT0iMTQiIGZvbnQtd2VpZ2h0PSJib2xkIj7gpJfgpKPgpYXgpLbgpKc8L3RleHQ+CjxwYXRoIGQ9Ik03MCA3MEwxMzAgNzBMMTMwIDEzMEw3MCAxMzBaIiBmaWxsPSJub25lIiBzdHJva2U9IiNGRkRCNTgiIHN0cm9rZS13aWR0aD0iMS41IiBzdHJva2UtZGFzaGFycmF5PSI2LDMiLz4KPC9zdmc+'
  }
];

function App() {
  const [selectedTemplate, setSelectedTemplate] = useState<Template>(templates[0]);
  const [invitationData, setInvitationData] = useState<InvitationData>({
    brideName: 'प्रिया',
    groomName: 'राहुल',
    ceremonyType: 'विवाह समारंभ',
    date: '2024-12-15',
    time: '10:00',
    venue: 'श्री गणेश मंदिर',
    address: 'मुख्य रस्ता, पुणे, महाराष्ट्र - ४११००१',
    hostFamily: 'शर्मा कुटुंब',
    additionalInfo: 'सादर आमंत्रण',
    selectedGanesha: 'ganesha1',
    motherName: 'सुनीता शर्मा',
    fatherName: 'राजेश शर्मा',
    groomMotherName: 'मीरा पाटील',
    groomFatherName: 'विकास पाटील',
    mehendi: { date: '2024-12-13', time: '16:00', venue: 'घर' },
    haldi: { date: '2024-12-14', time: '09:00', venue: 'घर' },
    reception: { date: '2024-12-15', time: '19:00', venue: 'हॉटेल ताज' }
  });

  const canvasRef = useRef<HTMLCanvasElement>(null);

  const handleInputChange = (field: keyof InvitationData, value: string) => {
    setInvitationData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleNestedInputChange = (parent: string, field: string, value: string) => {
    setInvitationData(prev => ({
      ...prev,
      [parent]: {
        ...prev[parent as keyof InvitationData] as any,
        [field]: value
      }
    }));
  };

  const getCurrentCeremony = () => {
    return ceremonyTypes.find(c => c.value === invitationData.ceremonyType) || ceremonyTypes[0];
  };

  const generateCanvas = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    canvas.width = 800;
    canvas.height = 1200;

    // Create gradient background
    const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
    gradient.addColorStop(0, selectedTemplate.colors.secondary);
    gradient.addColorStop(0.5, selectedTemplate.colors.primary);
    gradient.addColorStop(1, selectedTemplate.colors.accent);

    // Fill background
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Add background pattern if available
    if (selectedTemplate.backgroundImage) {
      const img = new Image();
      img.onload = () => {
        ctx.globalAlpha = 0.3;
        const pattern = ctx.createPattern(img, 'repeat');
        if (pattern) {
          ctx.fillStyle = pattern;
          ctx.fillRect(0, 0, canvas.width, canvas.height);
        }
        ctx.globalAlpha = 1.0;
        drawContent();
      };
      img.src = selectedTemplate.backgroundImage;
    } else {
      drawContent();
    }

    function drawContent() {
      // Enhanced decorative border with multiple layers
      const borderStyle = selectedTemplate.borderStyle || 'solid';

      // Outer decorative border
      ctx.strokeStyle = selectedTemplate.colors.accent;
      ctx.lineWidth = 12;
      if (borderStyle === 'double') {
        ctx.strokeRect(15, 15, canvas.width - 30, canvas.height - 30);
        ctx.lineWidth = 4;
        ctx.strokeRect(25, 25, canvas.width - 50, canvas.height - 50);
      } else if (borderStyle === 'ridge') {
        ctx.shadowColor = selectedTemplate.colors.accent;
        ctx.shadowBlur = 8;
        ctx.strokeRect(20, 20, canvas.width - 40, canvas.height - 40);
        ctx.shadowBlur = 0;
      } else {
        ctx.strokeRect(20, 20, canvas.width - 40, canvas.height - 40);
      }

      // Inner border with pattern
      ctx.strokeStyle = selectedTemplate.colors.secondary;
      ctx.lineWidth = 3;
      ctx.setLineDash(borderStyle === 'dashed' ? [10, 5] : []);
      ctx.strokeRect(40, 40, canvas.width - 80, canvas.height - 80);
      ctx.setLineDash([]);

      // Corner decorations
      const decorativeElements = selectedTemplate.decorativeElements || ['🕉️'];
      ctx.font = '32px serif';
      ctx.textAlign = 'center';
      decorativeElements.forEach((element, index) => {
        const positions = [
          { x: 60, y: 60 },   // Top-left
          { x: canvas.width - 60, y: 60 },   // Top-right
          { x: 60, y: canvas.height - 60 },   // Bottom-left
          { x: canvas.width - 60, y: canvas.height - 60 }   // Bottom-right
        ];
        if (positions[index]) {
          ctx.fillStyle = selectedTemplate.colors.accent;
          ctx.fillText(element, positions[index].x, positions[index].y);
        }
      });

      // Add Ganesha image at top center
      const selectedGaneshaImage = ganeshaImages.find(img => img.id === invitationData.selectedGanesha);
      if (selectedGaneshaImage) {
        const ganeshaImg = new Image();
        ganeshaImg.onload = () => {
          const imgSize = 120;
          const imgX = (canvas.width - imgSize) / 2;
          const imgY = 80;
          ctx.drawImage(ganeshaImg, imgX, imgY, imgSize, imgSize);
        };
        ganeshaImg.src = selectedGaneshaImage.url;
      }

      // Add decorative elements
      ctx.fillStyle = selectedTemplate.colors.accent;
      ctx.font = 'bold 48px serif';
      ctx.textAlign = 'center';
      ctx.fillText('॥ श्री गणेशाय नमः ॥', canvas.width / 2, 240);

      // Enhanced title with shadow effect
      ctx.fillStyle = selectedTemplate.colors.text;
      ctx.font = 'bold 36px serif';
      ctx.shadowColor = 'rgba(0,0,0,0.3)';
      ctx.shadowBlur = 4;
      ctx.shadowOffsetX = 2;
      ctx.shadowOffsetY = 2;
      ctx.fillText(invitationData.ceremonyType, canvas.width / 2, 320);
      ctx.shadowBlur = 0;
      ctx.shadowOffsetX = 0;
      ctx.shadowOffsetY = 0;

      let yPosition = 300;

      // Names based on ceremony type
      ctx.font = 'bold 32px serif';
      if (invitationData.ceremonyType === 'मेहंदी समारंभ') {
        ctx.fillText(`${invitationData.brideName}`, canvas.width / 2, yPosition);
        yPosition += 40;
      } else {
        ctx.fillText(`${invitationData.brideName}`, canvas.width / 2, yPosition);
        yPosition += 40;
        if (invitationData.groomName) {
          ctx.fillText('आणि', canvas.width / 2, yPosition);
          yPosition += 40;
          ctx.fillText(`${invitationData.groomName}`, canvas.width / 2, yPosition);
          yPosition += 40;
        }
      }

      // Parent names for wedding/engagement
      if ((invitationData.ceremonyType === 'विवाह समारंभ' || invitationData.ceremonyType === 'साखरपुडा') &&
        (invitationData.fatherName || invitationData.motherName)) {
        ctx.font = '20px serif';
        yPosition += 20;
        if (invitationData.fatherName && invitationData.motherName) {
          ctx.fillText(`${invitationData.fatherName} आणि ${invitationData.motherName} यांची कन्या`, canvas.width / 2, yPosition);
        } else if (invitationData.motherName) {
          ctx.fillText(`${invitationData.motherName} यांची कन्या`, canvas.width / 2, yPosition);
        }
        yPosition += 30;

        if (invitationData.groomFatherName && invitationData.groomMotherName) {
          ctx.fillText(`${invitationData.groomFatherName} आणि ${invitationData.groomMotherName} यांचा पुत्र`, canvas.width / 2, yPosition);
        } else if (invitationData.groomMotherName) {
          ctx.fillText(`${invitationData.groomMotherName} यांचा पुत्र`, canvas.width / 2, yPosition);
        }
        yPosition += 40;
      }

      // Main ceremony details
      ctx.font = '24px serif';
      yPosition += 20;
      ctx.fillText(`दिनांक: ${invitationData.date}`, canvas.width / 2, yPosition);
      yPosition += 40;
      ctx.fillText(`वेळ: ${invitationData.time}`, canvas.width / 2, yPosition);
      yPosition += 60;

      // Venue
      ctx.font = 'bold 28px serif';
      ctx.fillText('स्थळ:', canvas.width / 2, yPosition);
      yPosition += 40;
      ctx.font = '24px serif';
      ctx.fillText(invitationData.venue, canvas.width / 2, yPosition);
      yPosition += 40;

      // Address (wrap text)
      if (invitationData.address) {
        const words = invitationData.address.split(' ');
        let line = '';
        for (let n = 0; n < words.length; n++) {
          const testLine = line + words[n] + ' ';
          const metrics = ctx.measureText(testLine);
          const testWidth = metrics.width;
          if (testWidth > canvas.width - 100 && n > 0) {
            ctx.fillText(line, canvas.width / 2, yPosition);
            line = words[n] + ' ';
            yPosition += 30;
          } else {
            line = testLine;
          }
        }
        ctx.fillText(line, canvas.width / 2, yPosition);
        yPosition += 50;
      }

      // Additional ceremonies for complete wedding
      if (invitationData.ceremonyType === 'संपूर्ण विवाह') {
        ctx.font = 'bold 24px serif';

        if (invitationData.mehendi?.date) {
          ctx.fillText('मेहंदी समारंभ:', canvas.width / 2, yPosition);
          yPosition += 30;
          ctx.font = '20px serif';
          ctx.fillText(`${invitationData.mehendi.date} - ${invitationData.mehendi.time}`, canvas.width / 2, yPosition);
          yPosition += 25;
          ctx.fillText(invitationData.mehendi.venue, canvas.width / 2, yPosition);
          yPosition += 40;
          ctx.font = 'bold 24px serif';
        }

        if (invitationData.haldi?.date) {
          ctx.fillText('हळदी समारंभ:', canvas.width / 2, yPosition);
          yPosition += 30;
          ctx.font = '20px serif';
          ctx.fillText(`${invitationData.haldi.date} - ${invitationData.haldi.time}`, canvas.width / 2, yPosition);
          yPosition += 25;
          ctx.fillText(invitationData.haldi.venue, canvas.width / 2, yPosition);
          yPosition += 40;
          ctx.font = 'bold 24px serif';
        }

        if (invitationData.reception?.date) {
          ctx.fillText('रिसेप्शन:', canvas.width / 2, yPosition);
          yPosition += 30;
          ctx.font = '20px serif';
          ctx.fillText(`${invitationData.reception.date} - ${invitationData.reception.time}`, canvas.width / 2, yPosition);
          yPosition += 25;
          ctx.fillText(invitationData.reception.venue, canvas.width / 2, yPosition);
          yPosition += 40;
        }
      }

      // Host family
      ctx.font = 'bold 26px serif';
      ctx.fillText('आमंत्रणकर्ता:', canvas.width / 2, yPosition);
      yPosition += 40;
      ctx.font = '24px serif';
      ctx.fillText(invitationData.hostFamily, canvas.width / 2, yPosition);
      yPosition += 60;

      // Additional info
      if (invitationData.additionalInfo) {
        ctx.font = '20px serif';
        ctx.fillText(invitationData.additionalInfo, canvas.width / 2, yPosition);
        yPosition += 40;
      }

      // Bottom message
      ctx.font = 'italic 22px serif';
      ctx.fillText('आपल्या शुभ उपस्थितीची प्रतीक्षा आहे', canvas.width / 2, canvas.height - 80);
    }
  };

  const downloadInvitation = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const link = document.createElement('a');
    link.download = `${invitationData.brideName}-${invitationData.groomName || 'ceremony'}-invitation.png`;
    link.href = canvas.toDataURL();
    link.click();
  };

  useEffect(() => {
    generateCanvas();
  }, [selectedTemplate, invitationData]);

  const renderDynamicFields = () => {
    const ceremony = getCurrentCeremony();
    const IconComponent = ceremony.icon;

    return (
      <div className="form-section bg-white rounded-xl shadow-lg p-8">
        <div className="flex items-center mb-6">
          <IconComponent className="w-6 h-6 text-orange-500 mr-3 floating-animation" />
          <h3 className="text-2xl font-bold gradient-text">समारंभाचे तपशील</h3>
        </div>

        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            समारंभाचा प्रकार
          </label>
          <select
            value={invitationData.ceremonyType}
            onChange={(e) => handleInputChange('ceremonyType', e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
          >
            {ceremonyTypes.map((ceremony) => (
              <option key={ceremony.value} value={ceremony.value}>
                {ceremony.label}
              </option>
            ))}
          </select>
        </div>

        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-4">
            गणेश प्रतिमा निवडा
          </label>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            {ganeshaImages.map((ganesha) => (
              <div
                key={ganesha.id}
                className={`cursor-pointer border-2 rounded-lg p-3 transition-all duration-200 hover:shadow-lg ${invitationData.selectedGanesha === ganesha.id
                  ? 'border-orange-500 bg-orange-50 shadow-md'
                  : 'border-gray-200 hover:border-orange-300'
                  }`}
                onClick={() => handleInputChange('selectedGanesha', ganesha.id)}
              >
                <img
                  src={ganesha.url}
                  alt={ganesha.name}
                  className="w-full h-20 object-contain mb-2"
                />
                <p className="text-xs text-center text-gray-600 font-medium">
                  {ganesha.name}
                </p>
              </div>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {ceremony.fields.includes('brideName') && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Users className="inline-block w-4 h-4 mr-2" />
                वधूचे नाव
              </label>
              <input
                type="text"
                value={invitationData.brideName}
                onChange={(e) => handleInputChange('brideName', e.target.value)}
                className="enhanced-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                placeholder="वधूचे नाव"
              />
            </div>
          )}

          {ceremony.fields.includes('groomName') && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Users className="inline-block w-4 h-4 mr-2" />
                वराचे नाव
              </label>
              <input
                type="text"
                value={invitationData.groomName}
                onChange={(e) => handleInputChange('groomName', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                placeholder="वराचे नाव"
              />
            </div>
          )}

          {ceremony.fields.includes('fatherName') && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                वधूच्या वडिलांचे नाव
              </label>
              <input
                type="text"
                value={invitationData.fatherName || ''}
                onChange={(e) => handleInputChange('fatherName', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                placeholder="वडिलांचे नाव"
              />
            </div>
          )}

          {ceremony.fields.includes('motherName') && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                वधूच्या आईचे नाव
              </label>
              <input
                type="text"
                value={invitationData.motherName}
                onChange={(e) => handleInputChange('motherName', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                placeholder="आईचे नाव"
              />
            </div>
          )}

          {ceremony.fields.includes('groomFatherName') && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                वराच्या वडिलांचे नाव
              </label>
              <input
                type="text"
                value={invitationData.groomFatherName || ''}
                onChange={(e) => handleInputChange('groomFatherName', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                placeholder="वराच्या वडिलांचे नाव"
              />
            </div>
          )}

          {ceremony.fields.includes('groomMotherName') && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                वराच्या आईचे नाव
              </label>
              <input
                type="text"
                value={invitationData.groomMotherName || ''}
                onChange={(e) => handleInputChange('groomMotherName', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                placeholder="वराच्या आईचे नाव"
              />
            </div>
          )}

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Calendar className="inline-block w-4 h-4 mr-2" />
              दिनांक
            </label>
            <input
              type="date"
              value={invitationData.date}
              onChange={(e) => handleInputChange('date', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Clock className="inline-block w-4 h-4 mr-2" />
              वेळ
            </label>
            <input
              type="time"
              value={invitationData.time}
              onChange={(e) => handleInputChange('time', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <MapPin className="inline-block w-4 h-4 mr-2" />
              स्थळ
            </label>
            <input
              type="text"
              value={invitationData.venue}
              onChange={(e) => handleInputChange('venue', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
              placeholder="समारंभाचे स्थळ"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              आमंत्रणकर्ता कुटुंब
            </label>
            <input
              type="text"
              value={invitationData.hostFamily}
              onChange={(e) => handleInputChange('hostFamily', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
              placeholder="आमंत्रणकर्ता कुटुंबाचे नाव"
            />
          </div>
        </div>

        <div className="mt-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            पूर्ण पत्ता
          </label>
          <textarea
            value={invitationData.address}
            onChange={(e) => handleInputChange('address', e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            rows={3}
            placeholder="पूर्ण पत्ता"
          />
        </div>

        {/* Additional ceremonies for complete wedding */}
        {ceremony.fields.includes('mehendi') && (
          <div className="mt-8 p-6 bg-pink-50 rounded-lg">
            <h4 className="text-lg font-semibold text-gray-800 mb-4">मेहंदी समारंभ</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <input
                type="date"
                value={invitationData.mehendi?.date || ''}
                onChange={(e) => handleNestedInputChange('mehendi', 'date', e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500"
                placeholder="दिनांक"
              />
              <input
                type="time"
                value={invitationData.mehendi?.time || ''}
                onChange={(e) => handleNestedInputChange('mehendi', 'time', e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500"
                placeholder="वेळ"
              />
              <input
                type="text"
                value={invitationData.mehendi?.venue || ''}
                onChange={(e) => handleNestedInputChange('mehendi', 'venue', e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500"
                placeholder="स्थळ"
              />
            </div>
          </div>
        )}

        {ceremony.fields.includes('haldi') && (
          <div className="mt-6 p-6 bg-yellow-50 rounded-lg">
            <h4 className="text-lg font-semibold text-gray-800 mb-4">हळदी समारंभ</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <input
                type="date"
                value={invitationData.haldi?.date || ''}
                onChange={(e) => handleNestedInputChange('haldi', 'date', e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500"
                placeholder="दिनांक"
              />
              <input
                type="time"
                value={invitationData.haldi?.time || ''}
                onChange={(e) => handleNestedInputChange('haldi', 'time', e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500"
                placeholder="वेळ"
              />
              <input
                type="text"
                value={invitationData.haldi?.venue || ''}
                onChange={(e) => handleNestedInputChange('haldi', 'venue', e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500"
                placeholder="स्थळ"
              />
            </div>
          </div>
        )}

        {ceremony.fields.includes('reception') && (
          <div className="mt-6 p-6 bg-purple-50 rounded-lg">
            <h4 className="text-lg font-semibold text-gray-800 mb-4">रिसेप्शन</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <input
                type="date"
                value={invitationData.reception?.date || ''}
                onChange={(e) => handleNestedInputChange('reception', 'date', e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500"
                placeholder="दिनांक"
              />
              <input
                type="time"
                value={invitationData.reception?.time || ''}
                onChange={(e) => handleNestedInputChange('reception', 'time', e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500"
                placeholder="वेळ"
              />
              <input
                type="text"
                value={invitationData.reception?.venue || ''}
                onChange={(e) => handleNestedInputChange('reception', 'venue', e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500"
                placeholder="स्थळ"
              />
            </div>
          </div>
        )}

        <div className="mt-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            अतिरिक्त माहिती (पर्यायी)
          </label>
          <textarea
            value={invitationData.additionalInfo}
            onChange={(e) => handleInputChange('additionalInfo', e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            rows={2}
            placeholder="कोणतीही अतिरिक्त माहिती"
          />
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 to-red-50">
      <div className="container mx-auto px-4 py-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold gradient-text mb-4 floating-animation">
            मराठी लग्न आमंत्रणपत्र जनरेटर
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            सुंदर आणि पारंपारिक आमंत्रणपत्र तयार करा ✨
          </p>
          <div className="mt-4 text-2xl">
            🕉️ 🪔 🌸 🦚 🌺
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Step 1: Form */}
          <div className="lg:col-span-1">
            <div className="sticky top-8">
              <div className="flex items-center mb-6">
                <div className="w-8 h-8 bg-orange-500 text-white rounded-full flex items-center justify-center font-bold mr-3">
                  1
                </div>
                <h2 className="text-2xl font-bold text-gray-800">माहिती भरा</h2>
              </div>
              {renderDynamicFields()}
            </div>
          </div>

          {/* Step 2: Template Selection */}
          <div className="lg:col-span-1">
            <div className="sticky top-8">
              <div className="flex items-center mb-6">
                <div className="w-8 h-8 bg-orange-500 text-white rounded-full flex items-center justify-center font-bold mr-3">
                  2
                </div>
                <h2 className="text-2xl font-bold text-gray-800">टेम्प्लेट निवडा</h2>
              </div>

              <div className="bg-white rounded-xl shadow-lg p-6">
                <div className="grid grid-cols-1 gap-4">
                  {templates.map((template) => (
                    <div
                      key={template.id}
                      className={`template-card relative cursor-pointer rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-all duration-300 transform hover:scale-105 ${selectedTemplate.id === template.id ? 'ring-4 ring-orange-500 pulse-glow' : ''
                        }`}
                      onClick={() => setSelectedTemplate(template)}
                    >
                      <div
                        className="h-32 p-4 flex flex-col justify-center items-center text-white relative"
                        style={{
                          background: template.pattern,
                          backgroundImage: template.backgroundImage ? `url(${template.backgroundImage})` : 'none',
                          backgroundBlendMode: 'overlay'
                        }}
                      >
                        <div
                          className="absolute inset-2 border-2 border-white border-opacity-50 rounded-lg"
                          style={{ borderStyle: template.borderStyle || 'solid' }}
                        ></div>
                        <Palette className="w-8 h-8 mb-2" />
                        <h3 className="text-lg font-bold text-center drop-shadow-lg">{template.name}</h3>
                        {template.decorativeElements && (
                          <div className="absolute top-1 right-1 text-xs opacity-80">
                            {template.decorativeElements[0]}
                          </div>
                        )}
                      </div>
                      {selectedTemplate.id === template.id && (
                        <div className="absolute top-2 right-2 bg-orange-500 text-white rounded-full p-1">
                          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Step 3: Preview and Download */}
          <div className="lg:col-span-1">
            <div className="sticky top-8">
              <div className="flex items-center mb-6">
                <div className="w-8 h-8 bg-orange-500 text-white rounded-full flex items-center justify-center font-bold mr-3">
                  3
                </div>
                <h2 className="text-2xl font-bold text-gray-800">पूर्वावलोकन</h2>
              </div>

              <div className="bg-white rounded-xl shadow-lg p-6">
                <div className="text-center mb-6">
                  <canvas
                    ref={canvasRef}
                    className="invitation-canvas max-w-full h-auto border border-gray-200 rounded-lg shadow-md"
                    style={{ maxHeight: '500px' }}
                  />
                </div>

                <div className="text-center space-y-4">
                  <button
                    onClick={downloadInvitation}
                    className="w-full bg-gradient-to-r from-green-500 to-green-600 text-white px-6 py-3 rounded-lg font-semibold hover:from-green-600 hover:to-green-700 transition-all duration-300 shadow-lg hover:shadow-xl"
                  >
                    <Download className="inline-block w-5 h-5 mr-2" />
                    डाउनलोड करा
                  </button>

                  <div className="text-sm text-gray-500">
                    <Eye className="inline-block w-4 h-4 mr-1" />
                    रिअल-टाइम पूर्वावलोकन
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default App;