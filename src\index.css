@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for enhanced invitation design */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Devanagari:wght@300;400;500;600;700;800;900&display=swap');

@layer base {
    body {
        font-family: 'Noto Sans Devanagari', sans-serif;
    }
}

@layer components {
    .invitation-canvas {
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        border-radius: 12px;
        background: linear-gradient(145deg, #ffffff, #f0f0f0);
        padding: 8px;
    }

    .template-card {
        position: relative;
        overflow: hidden;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .template-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
        transform: translateX(-100%);
        transition: transform 0.6s;
    }

    .template-card:hover::before {
        transform: translateX(100%);
    }

    .decorative-border {
        background-image:
            linear-gradient(45deg, transparent 24%, rgba(255, 215, 0, 0.3) 25%, rgba(255, 215, 0, 0.3) 26%, transparent 27%, transparent 74%, rgba(255, 215, 0, 0.3) 75%, rgba(255, 215, 0, 0.3) 76%, transparent 77%, transparent),
            linear-gradient(-45deg, transparent 24%, rgba(255, 215, 0, 0.3) 25%, rgba(255, 215, 0, 0.3) 26%, transparent 27%, transparent 74%, rgba(255, 215, 0, 0.3) 75%, rgba(255, 215, 0, 0.3) 76%, transparent 77%, transparent);
        background-size: 20px 20px;
    }

    .traditional-pattern {
        background-image:
            radial-gradient(circle at 25% 25%, rgba(255, 215, 0, 0.1) 2px, transparent 2px),
            radial-gradient(circle at 75% 75%, rgba(255, 215, 0, 0.1) 2px, transparent 2px);
        background-size: 40px 40px;
    }

    .form-section {
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    .enhanced-input {
        transition: all 0.3s ease;
        border: 2px solid transparent;
        background: linear-gradient(white, white) padding-box,
            linear-gradient(45deg, #ff6b35, #f7931e) border-box;
    }

    .enhanced-input:focus {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(255, 107, 53, 0.15);
    }

    .gradient-text {
        background: linear-gradient(45deg, #ff6b35, #f7931e);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .floating-animation {
        animation: float 3s ease-in-out infinite;
    }

    @keyframes float {

        0%,
        100% {
            transform: translateY(0px);
        }

        50% {
            transform: translateY(-10px);
        }
    }

    .pulse-glow {
        animation: pulse-glow 2s ease-in-out infinite alternate;
    }

    @keyframes pulse-glow {
        from {
            box-shadow: 0 0 20px rgba(255, 107, 53, 0.4);
        }

        to {
            box-shadow: 0 0 30px rgba(255, 107, 53, 0.6);
        }
    }
}